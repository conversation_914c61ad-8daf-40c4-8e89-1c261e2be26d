@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Swimming Club Color Palette */
    --background: 0 0% 100%;
    --foreground: 0 0% 20%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;

    /* Primary Blue: #0077B6 */
    --primary: 199 100% 36%;
    --primary-foreground: 0 0% 100%;

    /* Light Blue for secondary */
    --secondary: 196 100% 75%;
    --secondary-foreground: 210 100% 11%;

    /* Muted backgrounds */
    --muted: 196 100% 96%;
    --muted-foreground: 199 50% 40%;

    /* Teal accent: #00B4D8 */
    --accent: 194 100% 42%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 196 50% 90%;
    --input: 196 50% 90%;
    --ring: 199 100% 36%;

    /* Custom Swimming Club Colors */
    --primary-blue: 199 100% 36%; /* #0077B6 */
    --teal: 194 100% 42%; /* #00B4D8 */
    --light-blue: 196 100% 75%; /* #90E0EF */
    --dark-blue: 210 100% 11%; /* #03045E */
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary-blue)), hsl(var(--teal)));
    --gradient-light: linear-gradient(180deg, hsl(var(--light-blue) / 0.1), hsl(var(--background)));
    
    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary-blue) / 0.2);
    --shadow-card: 0 4px 20px -4px hsl(var(--primary-blue) / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}