import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Award, Users, Waves } from "lucide-react";
import heroPool from "@/assets/hero-pool.jpg";

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img
          src={heroPool}
          alt="Swimming Pool"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-dark-blue/80 to-primary/60"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-4xl">
          <div className="flex items-center space-x-2 mb-6">
            <Waves className="h-8 w-8 text-teal" />
            <span className="text-lg font-semibold text-white/90">
              Splash Masters Swimming Club
            </span>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
            Dive into
            <span className="text-teal block">Excellence</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl leading-relaxed">
            Join our community of passionate swimmers and unlock your aquatic potential. 
            From beginners to competitive athletes, we're here to guide your journey.
          </p>

          {/* Stats */}
          <div className="flex flex-wrap gap-8 mb-10">
            <div className="flex items-center space-x-3">
              <div className="bg-white/20 p-3 rounded-full">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">500+</div>
                <div className="text-white/80">Active Members</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="bg-white/20 p-3 rounded-full">
                <Award className="h-6 w-6 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">25+</div>
                <div className="text-white/80">Years Experience</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="bg-white/20 p-3 rounded-full">
                <Waves className="h-6 w-6 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">100+</div>
                <div className="text-white/80">Championships Won</div>
              </div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button variant="hero" size="lg" className="text-lg px-8 py-4">
              Join Our Club
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="hero-outline" size="lg" className="text-lg px-8 py-4">
              View Handbook
            </Button>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;