import { Waves, Mail, Phone, MapPin, Facebook, Instagram, Twitter } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: "Home", href: "#" },
    { name: "About Us", href: "#about" },
    { name: "Membership", href: "#membership" },
    { name: "Training", href: "#training" },
    { name: "Events", href: "#events" },
    { name: "Contact", href: "#contact" }
  ];

  const resources = [
    { name: "Club Handbook", href: "#handbook" },
    { name: "Forms & Documents", href: "#forms" },
    { name: "Pool Schedule", href: "#schedule" },
    { name: "Competition Results", href: "#results" },
    { name: "News & Updates", href: "#news" },
    { name: "FAQs", href: "#faq" }
  ];

  return (
    <footer className="bg-dark-blue text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Club Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <Waves className="h-8 w-8 text-teal" />
              <span className="text-2xl font-bold">Splash Masters</span>
            </div>
            <p className="text-white/80 mb-6 leading-relaxed">
              Dedicated to excellence in aquatic sports, fostering community, and developing swimmers of all levels since 1999.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="bg-white/10 p-2 rounded-full hover:bg-teal transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="bg-white/10 p-2 rounded-full hover:bg-teal transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="bg-white/10 p-2 rounded-full hover:bg-teal transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-teal">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-white/80 hover:text-teal transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-teal">Resources</h4>
            <ul className="space-y-3">
              {resources.map((resource, index) => (
                <li key={index}>
                  <a
                    href={resource.href}
                    className="text-white/80 hover:text-teal transition-colors"
                  >
                    {resource.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-teal">Contact Us</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-teal mt-1 flex-shrink-0" />
                <div className="text-white/80">
                  <div>123 Aquatic Center Drive</div>
                  <div>Swimming City, SC 12345</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-teal flex-shrink-0" />
                <span className="text-white/80">(555) 123-SWIM</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-teal flex-shrink-0" />
                <span className="text-white/80"><EMAIL></span>
              </div>
            </div>

            {/* Pool Hours */}
            <div className="mt-6">
              <h5 className="font-semibold text-white mb-3">Pool Hours</h5>
              <div className="text-sm text-white/80 space-y-1">
                <div>Mon-Fri: 5:00 AM - 10:00 PM</div>
                <div>Saturday: 6:00 AM - 8:00 PM</div>
                <div>Sunday: 8:00 AM - 6:00 PM</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-white/60 text-sm mb-4 md:mb-0">
            © {currentYear} Splash Masters Swimming Club. All rights reserved.
          </div>
          <div className="flex space-x-6 text-sm">
            <a href="#" className="text-white/60 hover:text-teal transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="text-white/60 hover:text-teal transition-colors">
              Terms of Service
            </a>
            <a href="#" className="text-white/60 hover:text-teal transition-colors">
              Accessibility
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;