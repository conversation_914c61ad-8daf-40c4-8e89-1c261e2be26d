import { Card, CardContent } from "@/components/ui/card";
import { Heart, Target, Trophy, Users } from "lucide-react";

const Mission = () => {
  const values = [
    {
      icon: Heart,
      title: "Passion",
      description: "We foster a deep love for swimming and aquatic sports in all our members."
    },
    {
      icon: Target,
      title: "Excellence",
      description: "We strive for continuous improvement and personal best achievements."
    },
    {
      icon: Users,
      title: "Community",
      description: "We build lasting friendships and support networks through our shared passion."
    },
    {
      icon: Trophy,
      title: "Achievement",
      description: "We celebrate every milestone, from first strokes to championship victories."
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-muted/30 to-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Our Mission & Values
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            At Splash Masters Swimming Club, we believe that swimming is more than just a sport. 
            It's a way of life that builds character, promotes health, and creates lifelong bonds.
          </p>
        </div>

        {/* Mission Statement */}
        <div className="mb-16">
          <Card className="bg-white/50 backdrop-blur-sm border-primary/20 shadow-elegant">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-3xl font-bold text-primary mb-6">Our Mission</h3>
              <p className="text-lg text-foreground leading-relaxed max-w-4xl mx-auto">
                To provide exceptional aquatic training, foster competitive excellence, and build a 
                supportive community where swimmers of all levels can achieve their personal best 
                while developing lifelong skills, friendships, and a passion for swimming.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Core Values */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <Card key={index} className="bg-white/70 backdrop-blur-sm border-primary/10 hover:shadow-card transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-primary to-teal w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-foreground mb-4">
                    {value.title}
                  </h4>
                  <p className="text-muted-foreground leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Vision Statement */}
        <div className="mt-16 text-center">
          <h3 className="text-3xl font-bold text-primary mb-6">Our Vision</h3>
          <p className="text-lg text-foreground max-w-3xl mx-auto leading-relaxed">
            To be the premier swimming club in our region, recognized for developing champions 
            in the pool and in life, while maintaining an inclusive environment that welcomes 
            swimmers from all backgrounds and skill levels.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Mission;