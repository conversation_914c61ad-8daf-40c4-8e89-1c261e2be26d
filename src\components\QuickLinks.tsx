import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BookOpen, Calendar, Users, Trophy, FileText, MapPin } from "lucide-react";

const QuickLinks = () => {
  const links = [
    {
      icon: BookOpen,
      title: "Club Handbook",
      description: "Complete guide to club rules, policies, and procedures",
      action: "View Handbook",
      href: "#handbook",
      color: "from-primary to-primary-blue"
    },
    {
      icon: Calendar,
      title: "Events & Training",
      description: "Practice schedules, competitions, and special events",
      action: "View Schedule",
      href: "#events",
      color: "from-teal to-accent"
    },
    {
      icon: Users,
      title: "Membership",
      description: "Join our swimming family and start your aquatic journey",
      action: "Join Now",
      href: "#membership",
      color: "from-primary to-teal"
    },
    {
      icon: Trophy,
      title: "Competitions",
      description: "Local, regional, and national swimming competitions",
      action: "Learn More",
      href: "#competitions",
      color: "from-accent to-light-blue"
    },
    {
      icon: FileText,
      title: "Forms & Documents",
      description: "Registration forms, medical clearances, and waivers",
      action: "Download",
      href: "#forms",
      color: "from-primary-blue to-primary"
    },
    {
      icon: MapPin,
      title: "Facilities & Location",
      description: "Pool locations, amenities, and facility information",
      action: "Visit Us",
      href: "#location",
      color: "from-teal to-primary"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-background to-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Quick Access
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to know about Splash Masters Swimming Club at your fingertips.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {links.map((link, index) => {
            const IconComponent = link.icon;
            return (
              <Card key={index} className="group bg-white/70 backdrop-blur-sm border-primary/10 hover:shadow-card transition-all duration-300 hover:-translate-y-2 overflow-hidden">
                <CardHeader className="relative">
                  <div className={`absolute inset-0 bg-gradient-to-br ${link.color} opacity-5 group-hover:opacity-10 transition-opacity`}></div>
                  <div className="relative flex items-center space-x-4">
                    <div className={`bg-gradient-to-br ${link.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-foreground group-hover:text-primary transition-colors">
                        {link.title}
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="relative">
                  <CardDescription className="text-muted-foreground mb-6 leading-relaxed">
                    {link.description}
                  </CardDescription>
                  <Button 
                    variant="outline" 
                    className="w-full group-hover:bg-primary group-hover:text-white group-hover:border-primary transition-all"
                    asChild
                  >
                    <a href={link.href}>{link.action}</a>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Featured Call-to-Action */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-primary to-teal border-0 text-white max-w-4xl mx-auto">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4">Ready to Make a Splash?</h3>
              <p className="text-xl mb-8 text-white/90">
                Join hundreds of swimmers who have made Splash Masters their aquatic home.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="hero-outline" size="lg" className="text-lg px-8">
                  Schedule a Tour
                </Button>
                <Button variant="secondary" size="lg" className="text-lg px-8">
                  Start Membership
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default QuickLinks;