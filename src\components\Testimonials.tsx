import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON>uo<PERSON>, <PERSON> } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Competitive Swimmer",
      content: "<PERSON><PERSON><PERSON> has transformed my swimming career. The coaching is world-class and the community support is incredible. I've achieved personal bests I never thought possible!",
      rating: 5,
      image: "https://images.unsplash.com/photo-1494790108755-2616b612c5f4?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "<PERSON>",
      role: "Parent & Member",
      content: "My daughter joined as a beginner and now she's competing at regional level. The club's focus on both skill development and character building is outstanding.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "<PERSON>",
      role: "Masters Swimmer",
      content: "As an adult returning to swimming, I was nervous. But the welcoming atmosphere and excellent coaching helped me rediscover my love for the sport.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            What Our Members Say
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our swimming family has to say about their experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-gradient-to-br from-white to-muted/20 border-primary/10 hover:shadow-card transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-8">
                <div className="flex items-center justify-between mb-6">
                  <Quote className="h-8 w-8 text-primary/60" />
                  <div className="flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>

                <p className="text-foreground leading-relaxed mb-6 text-lg">
                  "{testimonial.content}"
                </p>

                <div className="flex items-center space-x-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover border-2 border-primary/20"
                  />
                  <div>
                    <div className="font-bold text-foreground">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Stats */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="p-6">
            <div className="text-4xl font-bold text-primary mb-2">98%</div>
            <div className="text-muted-foreground">Member Satisfaction</div>
          </div>
          <div className="p-6">
            <div className="text-4xl font-bold text-primary mb-2">15+</div>
            <div className="text-muted-foreground">Average Years Membership</div>
          </div>
          <div className="p-6">
            <div className="text-4xl font-bold text-primary mb-2">85%</div>
            <div className="text-muted-foreground">Achieve Personal Goals</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;